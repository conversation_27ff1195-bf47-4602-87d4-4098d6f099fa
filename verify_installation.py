#!/usr/bin/env python3
"""
Verify that the daily sales webhook module is installed correctly
Run this in Odoo shell after installation to check everything is working
"""

def verify_installation():
    """Verify the installation is correct"""
    
    print("=" * 60)
    print("🔍 VERIFYING DAILY SALES WEBHOOK INSTALLATION")
    print("=" * 60)
    
    # Check if module is installed
    module = env['ir.module.module'].search([
        ('name', '=', 'daily_sales_webhook'),
        ('state', '=', 'installed')
    ])
    
    if module:
        print(f"✅ Module installed: {module.name} v{module.latest_version}")
    else:
        print("❌ Module not found or not installed")
        return
    
    # Check cron jobs
    print(f"\n📅 CHECKING CRON JOBS:")
    print("-" * 40)
    
    # Main cron job
    main_cron = env['ir.cron'].search([
        ('name', 'ilike', 'Daily Sales Webhook Reports')
    ], limit=1)
    
    if main_cron:
        print(f"✅ Main Cron Job: {main_cron.name}")
        print(f"   Interval: {main_cron.interval_number} {main_cron.interval_type}")
        print(f"   Code: {main_cron.code}")
        print(f"   Active: {main_cron.active}")
        print(f"   Next call: {main_cron.nextcall}")
        
        # Check if configuration is correct
        if main_cron.interval_number == 1 and main_cron.interval_type == 'days':
            print("   ✅ Interval configuration: CORRECT")
        else:
            print("   ❌ Interval configuration: INCORRECT")
            
        if 'single_company' in main_cron.code:
            print("   ✅ Method configuration: CORRECT")
        else:
            print("   ❌ Method configuration: INCORRECT")
            
        if main_cron.active:
            print("   ✅ Status: ACTIVE")
        else:
            print("   ❌ Status: INACTIVE")
    else:
        print("❌ Main cron job not found")
    
    # Retry cron job
    retry_cron = env['ir.cron'].search([
        ('name', 'ilike', 'Retry Failed Webhooks')
    ], limit=1)
    
    if retry_cron:
        print(f"\n✅ Retry Cron Job: {retry_cron.name}")
        print(f"   Interval: {retry_cron.interval_number} {retry_cron.interval_type}")
        print(f"   Active: {retry_cron.active}")
    else:
        print("\n❌ Retry cron job not found")
    
    # Check models
    print(f"\n🏗️ CHECKING MODELS:")
    print("-" * 40)
    
    try:
        service = env['daily.sales.webhook.service']
        print("✅ Webhook service model: EXISTS")
        
        # Test if methods exist
        if hasattr(service, 'process_daily_reports_single_company'):
            print("✅ Single company method: EXISTS")
        else:
            print("❌ Single company method: MISSING")
            
    except Exception as e:
        print(f"❌ Webhook service model: ERROR - {str(e)}")
    
    try:
        report_model = env['daily.sales.report']
        print("✅ Daily sales report model: EXISTS")
    except Exception as e:
        print(f"❌ Daily sales report model: ERROR - {str(e)}")
    
    # Check company fields
    print(f"\n🏢 CHECKING COMPANY FIELDS:")
    print("-" * 40)
    
    try:
        company = env['res.company'].search([], limit=1)
        if company:
            fields_to_check = [
                'daily_webhook_enabled',
                'daily_webhook_url', 
                'daily_webhook_hour',
                'daily_webhook_minute',
                'daily_webhook_timezone'
            ]
            
            for field in fields_to_check:
                if hasattr(company, field):
                    print(f"✅ Field {field}: EXISTS")
                else:
                    print(f"❌ Field {field}: MISSING")
        else:
            print("❌ No company found")
            
    except Exception as e:
        print(f"❌ Error checking company fields: {str(e)}")
    
    # Overall status
    print(f"\n" + "=" * 60)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 60)
    
    if main_cron and main_cron.active and main_cron.interval_type == 'days':
        print("✅ INSTALLATION SUCCESSFUL!")
        print("   - Module installed correctly")
        print("   - Cron job active and configured for daily execution")
        print("   - All models and fields present")
        print("\n🎯 NEXT STEPS:")
        print("   1. Go to Settings → Companies → [Company] → Daily Webhook Settings")
        print("   2. Enable webhook and configure URL/timing")
        print("   3. Test with 'Test Cron Job' button")
    else:
        print("❌ INSTALLATION ISSUES DETECTED!")
        print("   Please check the errors above and fix them")
        print("\n🔧 POSSIBLE SOLUTIONS:")
        print("   1. Reinstall the module")
        print("   2. Use 'Fix Cron Job' button in company settings")
        print("   3. Run fix_cron_job_immediately.py script")

# Run verification
if __name__ == "__main__":
    verify_installation()
