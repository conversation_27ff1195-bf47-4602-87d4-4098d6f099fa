# -*- coding: utf-8 -*-

from . import models

def post_init_hook(cr, registry):
    """Post-installation hook to configure cron job properly"""
    import logging
    from odoo import api, SUPERUSER_ID

    _logger = logging.getLogger(__name__)

    with api.Environment.manage():
        env = api.Environment(cr, SUPERUSER_ID, {})

        try:
            # Find the main webhook cron job
            cron_job = env['ir.cron'].search([
                ('name', 'ilike', 'Daily Sales Webhook Reports')
            ], limit=1)

            if cron_job:
                # Ensure it's configured correctly for daily execution
                cron_job.sudo().write({
                    'interval_number': 1,
                    'interval_type': 'days',
                    'code': 'model.process_daily_reports_single_company()',
                    'active': True,  # Active by default, will be managed by webhook enable/disable
                })

                _logger.info("✅ Daily Sales Webhook cron job configured correctly during installation")
                _logger.info(f"   - Interval: 1 days")
                _logger.info(f"   - Code: model.process_daily_reports_single_company()")
                _logger.info(f"   - Active: True")
                _logger.info(f"   - Next call: {cron_job.nextcall}")
            else:
                _logger.warning("❌ Daily Sales Webhook cron job not found during post-install")

        except Exception as e:
            _logger.error(f"❌ Error configuring cron job during installation: {str(e)}")
