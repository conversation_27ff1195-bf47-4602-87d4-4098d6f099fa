# -*- coding: utf-8 -*-

from . import models

def post_init_hook(env):
    """Post-installation hook to configure cron job properly"""
    try:
        # Find the main webhook cron job
        cron_job = env['ir.cron'].search([
            ('name', 'ilike', 'Daily Sales Webhook Reports')
        ], limit=1)

        if cron_job:
            # Ensure it's configured correctly for daily execution
            cron_job.sudo().write({
                'interval_number': 1,
                'interval_type': 'days',
                'code': 'model.process_daily_reports_single_company()',
                'active': True,  # Active by default
            })

    except Exception as e:
        # Log only critical errors
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f"Error configuring cron job during installation: {str(e)}")
