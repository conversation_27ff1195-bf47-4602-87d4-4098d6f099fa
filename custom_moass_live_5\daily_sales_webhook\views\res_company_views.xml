<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Company Form View - Add Webhook Configuration -->
        <record id="view_company_form_webhook" model="ir.ui.view">
            <field name="name">res.company.form.webhook</field>
            <field name="model">res.company</field>
            <field name="inherit_id" ref="base.view_company_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Daily Webhook Settings" name="daily_webhook">
                        <group>
                            <group string="Webhook Configuration">
                                <field name="daily_webhook_enabled"/>
                                <field name="daily_webhook_url"
                                       required="daily_webhook_enabled"
                                       invisible="not daily_webhook_enabled"/>
                                <field name="daily_webhook_include_details"
                                       invisible="not daily_webhook_enabled"/>
                                <button name="action_test_webhook"
                                        string="Test Webhook"
                                        type="object"
                                        class="btn-primary"
                                        invisible="not daily_webhook_enabled or not daily_webhook_url"/>
                                <button name="action_force_daily_report"
                                        string="Send Daily Report Now"
                                        type="object"
                                        class="btn-secondary"
                                        invisible="not daily_webhook_enabled or not daily_webhook_url"/>
                                <button name="action_debug_webhook"
                                        string="Debug Webhook"
                                        type="object"
                                        class="btn-info"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_test_cron_job"
                                        string="Test Cron Job"
                                        type="object"
                                        class="btn-warning"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_reset_daily_webhook"
                                        string="Reset Today's Webhook"
                                        type="object"
                                        class="btn-danger"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_debug_daily_data"
                                        string="Debug Daily Data"
                                        type="object"
                                        class="btn-info"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_clear_debug_info"
                                        string="Clear Debug Info"
                                        type="object"
                                        class="btn-secondary"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_update_cron_schedule"
                                        string="Update Cron Schedule"
                                        type="object"
                                        class="btn-success"
                                        invisible="not daily_webhook_enabled"/>
                                <button name="action_fix_cron_job"
                                        string="Fix Cron Job"
                                        type="object"
                                        class="btn-warning"
                                        help="Fix cron job if it's running every minute instead of daily"/>
                            </group>
                            <group string="Scheduling">
                                <field name="daily_webhook_timezone"
                                       invisible="not daily_webhook_enabled"/>
                                <field name="daily_webhook_hour"
                                       invisible="not daily_webhook_enabled"/>
                                <field name="daily_webhook_minute"
                                       invisible="not daily_webhook_enabled"/>
                                <field name="daily_webhook_test_date"
                                       invisible="not daily_webhook_enabled"/>
                            </group>
                        </group>
                        <group>
                            <group string="Retry Configuration">
                                <field name="daily_webhook_retry_enabled"
                                       invisible="not daily_webhook_enabled"/>
                                <field name="daily_webhook_retry_count"
                                       invisible="not daily_webhook_enabled or not daily_webhook_retry_enabled"/>
                                <field name="daily_webhook_retry_delay"
                                       invisible="not daily_webhook_enabled or not daily_webhook_retry_enabled"/>
                            </group>
                            <group string="Status Information">
                                <field name="daily_webhook_last_sent" readonly="True"/>
                                <field name="daily_webhook_last_error" readonly="True"
                                       invisible="not daily_webhook_last_error"/>
                            </group>
                        </group>

                    </page>
                </xpath>
            </field>
        </record>
        
    </data>
</odoo>
