# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import requests
import json
import time
import logging
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

_logger = logging.getLogger(__name__)


class DailySalesWebhookService(models.AbstractModel):
    _name = 'daily.sales.webhook.service'
    _description = 'Daily Sales Webhook Service'

    @api.model
    def _get_session_with_retries(self):
        """Create a requests session with retry configuration"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"],
            backoff_factor=1
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session

    @api.model
    def send_webhook_data(self, company, data, retry_count=0):
        """
        Send data to webhook URL with retry mechanism
        
        Args:
            company: res.company record
            data: dict with data to send
            retry_count: current retry attempt
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not company.daily_webhook_url:
            return False
        
        max_retries = company.daily_webhook_retry_count or 3
        retry_delay = company.daily_webhook_retry_delay or 5
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': f'Odoo-Daily-Sales-Webhook/1.0 (Database: {self.env.cr.dbname})',
        }
        
        try:
            session = self._get_session_with_retries()

            response = session.post(
                company.daily_webhook_url,
                json=data,
                headers=headers,
                timeout=30  # 30 seconds timeout
            )

            # Check if request was successful
            if response.status_code in [200, 201, 202]:
                return True
            else:
                # Retry if we haven't exceeded max retries
                if retry_count < max_retries:
                    time.sleep(retry_delay * 60)  # Convert minutes to seconds
                    return self.send_webhook_data(company, data, retry_count + 1)

                return False
                
        except requests.exceptions.Timeout:
            if retry_count < max_retries:
                time.sleep(retry_delay * 60)
                return self.send_webhook_data(company, data, retry_count + 1)
            return False

        except requests.exceptions.ConnectionError:
            if retry_count < max_retries:
                time.sleep(retry_delay * 60)
                return self.send_webhook_data(company, data, retry_count + 1)
            return False

        except requests.exceptions.RequestException:
            if retry_count < max_retries:
                time.sleep(retry_delay * 60)
                return self.send_webhook_data(company, data, retry_count + 1)
            return False

        except Exception:
            return False

    @api.model
    def process_daily_reports_single_company(self):
        """
        Process daily reports for single company setup
        This method is called by the daily cron job at the scheduled time
        """
        # Get the company with webhook enabled (should be only one)
        company = self.env['res.company'].search([
            ('daily_webhook_enabled', '=', True),
            ('daily_webhook_url', '!=', False),
        ], limit=1)

        if not company:
            return {"processed": 0, "success": 0, "errors": 0, "message": "No company has webhook enabled"}

        try:
            report_model = self.env['daily.sales.report'].sudo()

            # Create or update daily report
            report = report_model.create_daily_report(company.id)

            # Send webhook if not already sent today
            if not report.webhook_sent:
                success = report.send_webhook()

                if success:
                    return {"processed": 1, "success": 1, "errors": 0, "message": "Daily report sent successfully"}
                else:
                    return {"processed": 1, "success": 0, "errors": 1, "message": "Webhook sending failed"}
            else:
                return {"processed": 1, "success": 1, "errors": 0, "message": "Webhook already sent today"}

        except Exception as e:
            error_msg = f"Error processing daily report for company {company.name}: {str(e)}"
            _logger.error(error_msg)  # Keep error logging for debugging
            company.daily_webhook_last_error = error_msg
            return {"processed": 1, "success": 0, "errors": 1, "message": error_msg}

    @api.model
    def process_daily_reports(self, force_all=False):
        """
        Legacy method for backward compatibility and manual testing
        """
        # Get all companies with webhook enabled
        companies = self.env['res.company'].search([
            ('daily_webhook_enabled', '=', True),
            ('daily_webhook_url', '!=', False),
        ])

        if not companies:
            return {"processed": 0, "success": 0, "errors": 0, "message": "No companies configured"}

        report_model = self.env['daily.sales.report'].sudo()
        success_count = 0
        error_count = 0
        processed_count = 0

        for company in companies:
            try:
                processed_count += 1

                # Create or update daily report
                report = report_model.create_daily_report(company.id)

                # Send webhook if not already sent OR if force_all is True
                if not report.webhook_sent or force_all:
                    success = report.send_webhook()
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    success_count += 1

            except Exception as e:
                error_count += 1
                error_msg = f"Error processing daily report for company {company.name}: {str(e)}"
                _logger.error(error_msg)  # Keep error logging
                company.daily_webhook_last_error = error_msg

        return {
            "processed": processed_count,
            "success": success_count,
            "errors": error_count,
            "message": f"Processed {processed_count} companies, {success_count} successful, {error_count} errors"
        }

    @api.model
    def retry_failed_webhooks(self):
        """
        Retry failed webhooks that haven't exceeded max retry count
        Optimized for single company setup with limited retry attempts
        """
        # Check if any company has retry enabled
        companies_with_retry = self.env['res.company'].search([
            ('daily_webhook_enabled', '=', True),
            ('daily_webhook_retry_enabled', '=', True)
        ])

        if not companies_with_retry:
            return

        # Find reports with failed webhooks from the last 24 hours only
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(days=1)

        failed_reports = self.env['daily.sales.report'].sudo().search([
            ('webhook_sent', '=', False),
            ('webhook_error', '!=', False),
            ('company_id.daily_webhook_enabled', '=', True),
            ('company_id.daily_webhook_url', '!=', False),
            ('company_id.daily_webhook_retry_enabled', '=', True),
            ('report_date', '>=', yesterday.date()),  # Only retry recent failures
        ])

        if not failed_reports:
            return

        retry_count = 0
        success_count = 0

        for report in failed_reports:
            max_retries = report.company_id.daily_webhook_retry_count or 2  # Reduced default retries

            if report.webhook_retry_count < max_retries:
                success = report.send_webhook()
                retry_count += 1

                if success:
                    success_count += 1

    @api.model
    def force_process_daily_reports(self):
        """
        Force process daily reports for all companies (ignores timing)
        This method can be called manually for testing
        """
        return self.process_daily_reports(force_all=True)

    @api.model
    def debug_cron_execution(self):
        """
        Debug cron execution - show detailed timing and configuration info for single company
        """
        _logger.info("🔍 DEBUG: Starting cron execution debug (Single Company Mode)")

        # Get the company with webhook enabled
        company = self.env['res.company'].search([
            ('daily_webhook_enabled', '=', True),
            ('daily_webhook_url', '!=', False),
        ], limit=1)

        if not company:
            _logger.info("❌ No company configured for webhook")
            return

        try:
            # Get current time in company's timezone
            company_current_time = company._get_current_time_in_company_timezone()
            current_hour = company_current_time.hour
            current_minute = company_current_time.minute

            # Get company's scheduled time
            company_hour = company.daily_webhook_hour or 23
            company_minute = company.daily_webhook_minute or 30

            # Get next execution time
            next_execution = company._calculate_next_execution_time()

            # Check cron job status
            cron_job = self.env['ir.cron'].search([
                ('name', 'ilike', 'Daily Sales Webhook Reports')
            ], limit=1)

            _logger.info(f"🏢 Company: {company.name}")
            _logger.info(f"   Timezone: {company.daily_webhook_timezone or 'UTC'}")
            _logger.info(f"   Current time: {current_hour:02d}:{current_minute:02d} ({company.daily_webhook_timezone or 'UTC'})")
            _logger.info(f"   Scheduled time: {company_hour:02d}:{company_minute:02d} ({company.daily_webhook_timezone or 'UTC'})")
            _logger.info(f"   Next execution: {next_execution} UTC")
            _logger.info(f"   Webhook URL: {company.daily_webhook_url or 'NOT SET'}")
            _logger.info(f"   Last sent: {company.daily_webhook_last_sent or 'NEVER'}")

            if cron_job:
                _logger.info(f"📅 Cron Job Status:")
                _logger.info(f"   Active: {cron_job.active}")
                _logger.info(f"   Next call: {cron_job.nextcall}")
                _logger.info(f"   Interval: {cron_job.interval_number} {cron_job.interval_type}")
            else:
                _logger.warning("❌ Cron job not found!")

        except Exception as e:
            _logger.error(f"❌ Error debugging company {company.name}: {str(e)}")

        _logger.info("🔍 DEBUG: Cron execution debug completed")



    @api.model
    def validate_webhook_url(self, url):
        """
        Validate webhook URL by sending a test request
        
        Args:
            url: webhook URL to validate
            
        Returns:
            dict: {'success': bool, 'message': str}
        """
        if not url:
            return {'success': False, 'message': 'URL is required'}
        
        test_data = {
            'test_mode': True,
            'message': 'This is a test webhook from Odoo',
            'timestamp': self.env['ir.fields'].datetime.now().isoformat(),
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': f'Odoo-Daily-Sales-Webhook/1.0 (Test)',
        }
        
        try:
            response = requests.post(
                url,
                json=test_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code in [200, 201, 202]:
                return {'success': True, 'message': f'Webhook test successful (Status: {response.status_code})'}
            else:
                return {'success': False, 'message': f'Webhook test failed (Status: {response.status_code})'}
                
        except requests.exceptions.Timeout:
            return {'success': False, 'message': 'Webhook test timed out'}
        except requests.exceptions.ConnectionError:
            return {'success': False, 'message': 'Connection error - check URL and network'}
        except Exception as e:
            return {'success': False, 'message': f'Test failed: {str(e)}'}
