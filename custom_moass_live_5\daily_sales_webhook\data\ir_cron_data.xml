<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        
        <!-- Daily Sales Webhook Cron Job (runs once per day at user's specified time) -->
        <record id="ir_cron_daily_sales_webhook" model="ir.cron">
            <field name="name">Daily Sales Webhook Reports</field>
            <field name="model_id" ref="model_daily_sales_webhook_service"/>
            <field name="state">code</field>
            <field name="code">model.process_daily_reports_single_company()</field>
            <field name="active" eval="True"/>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="nextcall" eval="(DateTime.now() + timedelta(days=1)).replace(hour=23, minute=30, second=0, microsecond=0)"/>
        </record>

        <!-- Webhook Retry Cron Job (runs every 2 hours to retry failed webhooks) -->
        <record id="ir_cron_webhook_retry" model="ir.cron">
            <field name="name">Retry Failed Webhooks</field>
            <field name="model_id" ref="model_daily_sales_webhook_service"/>
            <field name="state">code</field>
            <field name="code">model.retry_failed_webhooks()</field>
            <field name="active" eval="True"/>
            <field name="interval_number">2</field>
            <field name="interval_type">hours</field>
        </record>
        
    </data>
</odoo>
