#!/usr/bin/env python3
"""
IMMEDIATE FIX: Stop the every-minute cron job and set it to daily
Run this in Odoo shell to fix the cron job configuration immediately
"""

def fix_cron_job_now():
    """Fix the cron job configuration immediately"""
    
    print("=" * 60)
    print("🔧 FIXING CRON JOB CONFIGURATION")
    print("=" * 60)
    
    # Find the main webhook cron job
    cron_job = env['ir.cron'].search([
        ('name', 'ilike', 'Daily Sales Webhook Reports')
    ], limit=1)
    
    if not cron_job:
        print("❌ Cron job not found!")
        return
    
    print(f"📊 Found cron job: {cron_job.name}")
    print(f"   Current interval: {cron_job.interval_number} {cron_job.interval_type}")
    print(f"   Current code: {cron_job.code}")
    print(f"   Active: {cron_job.active}")
    print(f"   Next call: {cron_job.nextcall}")
    
    # Check if it's the problematic every-minute configuration
    if cron_job.interval_number == 1 and cron_job.interval_type == 'minutes':
        print("\n🚨 PROBLEM DETECTED: Cron job is running every minute!")
        print("🔧 Fixing configuration...")
        
        # Get company with webhook enabled
        company = env['res.company'].search([
            ('daily_webhook_enabled', '=', True)
        ], limit=1)
        
        if company:
            # Calculate next execution time
            from datetime import datetime, timedelta
            import pytz
            
            # Get company timezone
            company_tz = pytz.timezone(company.daily_webhook_timezone or 'UTC')
            
            # Get current time in company timezone
            utc_now = pytz.UTC.localize(datetime.utcnow())
            company_now = utc_now.astimezone(company_tz)
            
            # Create target time for today in company timezone
            target_time = company_now.replace(
                hour=company.daily_webhook_hour or 23,
                minute=company.daily_webhook_minute or 30,
                second=0,
                microsecond=0
            )
            
            # If target time has passed today, schedule for tomorrow
            if target_time <= company_now:
                target_time += timedelta(days=1)
            
            # Convert back to UTC for cron job
            target_utc = target_time.astimezone(pytz.UTC)
            next_execution = target_utc.replace(tzinfo=None)
            
            # Fix the cron job
            cron_job.sudo().write({
                'interval_number': 1,
                'interval_type': 'days',
                'code': 'model.process_daily_reports_single_company()',
                'nextcall': next_execution,
                'active': True,
            })
            
            print("✅ CRON JOB FIXED!")
            print(f"   New interval: 1 days")
            print(f"   New code: model.process_daily_reports_single_company()")
            print(f"   Next execution: {next_execution} UTC")
            print(f"   Company time: {company.daily_webhook_hour:02d}:{company.daily_webhook_minute:02d} ({company.daily_webhook_timezone or 'UTC'})")
            print("\n🎉 The cron job will now run once per day at your scheduled time!")
            
        else:
            print("❌ No company with webhook enabled found!")
            print("   Please enable webhook in company settings first.")
    
    else:
        print("✅ Cron job is already configured correctly!")
        print(f"   Interval: {cron_job.interval_number} {cron_job.interval_type}")
        print("   No changes needed.")
    
    print("\n" + "=" * 60)
    print("🔧 CRON JOB FIX COMPLETED")
    print("=" * 60)

def check_cron_status():
    """Check current cron job status"""
    
    print("\n" + "=" * 60)
    print("📊 CHECKING CRON JOB STATUS")
    print("=" * 60)
    
    # Find all webhook-related cron jobs
    cron_jobs = env['ir.cron'].search([
        '|',
        ('name', 'ilike', 'Daily Sales Webhook'),
        ('name', 'ilike', 'Retry Failed Webhooks')
    ])
    
    for cron in cron_jobs:
        print(f"\n📋 {cron.name}:")
        print(f"   Interval: {cron.interval_number} {cron.interval_type}")
        print(f"   Code: {cron.code}")
        print(f"   Active: {cron.active}")
        print(f"   Next call: {cron.nextcall}")
        print(f"   Last call: {cron.lastcall}")
        
        if cron.interval_number == 1 and cron.interval_type == 'minutes':
            print("   🚨 WARNING: This cron job runs every minute!")
        else:
            print("   ✅ Configuration looks good")

# Run the fix
if __name__ == "__main__":
    check_cron_status()
    fix_cron_job_now()
    check_cron_status()
