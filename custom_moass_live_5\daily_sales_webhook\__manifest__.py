# -*- coding: utf-8 -*-
{
    'name': 'Daily Sales & Payment Webhook',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Send daily sales and payment data to n8n webhook for WhatsApp automation',
    'description': """
Daily Sales & Payment Webhook
=============================

Simple module that automatically sends daily financial data to n8n webhook for WhatsApp automation.

Features:
---------
* Daily sales, customer payments, vendor payments, and expenses
* Cash and bank balances with detailed breakdown
* Expense details by account
* Configurable webhook URL and timing per company with timezone support
* Automatic retry mechanism for failed webhook calls
* Timezone-aware scheduling for accurate report timing

Configuration:
--------------
1. Go to Settings > Companies > Company Settings > Daily Webhook Settings
2. Enable webhook and configure URL, timezone, and timing
3. Test the webhook connection
4. Reports are sent automatically at the configured time in the specified timezone

Data Sent:
----------
* Total sales, customer payments, vendor payments, expenses
* Cash balances (total + breakdown by journal)
* Bank balances (total + breakdown by account)
* Expense details (by account name and amount)
    """,
    'author': 'Cubes Company',
    'website': 'https://www.erp.cubes.ly',
    'depends': [
        'base',
        'account',
        'sale',
        'base_setup',
    ],
    'data': [
        'security/daily_webhook_security.xml',
        'security/ir.model.access.csv',
        'data/ir_cron_data.xml',
        'views/res_company_views.xml',
    ],
   
    'post_init_hook': 'post_init_hook',
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
