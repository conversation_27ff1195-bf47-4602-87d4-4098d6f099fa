# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import re
import pytz


class ResCompany(models.Model):
    _inherit = 'res.company'

    # Webhook Configuration
    daily_webhook_url = fields.Char(
        string='Daily Report Webhook URL',
        help='n8n webhook URL to send daily sales and payment data'
    )
    
    daily_webhook_enabled = fields.Boolean(
        string='Enable Daily Webhook',
        default=False,
        help='Enable automatic daily webhook reports'
    )
    
    # Scheduling Configuration
    daily_webhook_hour = fields.Integer(
        string='Report Hour',
        default=23,
        help='Hour of the day to send the report (0-23) in company timezone'
    )

    daily_webhook_minute = fields.Integer(
        string='Report Minute',
        default=30,
        help='Minute of the hour to send the report (0-59) in company timezone'
    )

    daily_webhook_timezone = fields.Selection(
        string='Report Timezone',
        selection='_get_timezone_selection',
        default='UTC',
        help='Timezone for the scheduled report time'
    )
    
    # Retry Configuration
    daily_webhook_retry_enabled = fields.Boolean(
        string='Enable Retry',
        default=True,
        help='Enable automatic retry for failed webhook calls'
    )

    daily_webhook_retry_count = fields.Integer(
        string='Retry Count',
        default=3,
        help='Number of retry attempts for failed webhook calls'
    )

    daily_webhook_retry_delay = fields.Integer(
        string='Retry Delay (minutes)',
        default=5,
        help='Delay between retry attempts in minutes'
    )
    
    # Last Report Information
    daily_webhook_last_sent = fields.Datetime(
        string='Last Report Sent',
        readonly=True,
        help='Timestamp of the last successful webhook report'
    )
    
    daily_webhook_last_error = fields.Text(
        string='Last Error',
        readonly=True,
        help='Last error message from webhook call'
    )

    daily_webhook_include_details = fields.Boolean(
        string='Include Bank/Cash Details',
        default=False,
        help='Include detailed bank and cash balances in webhook data. If disabled, only totals are sent.'
    )

    daily_webhook_test_date = fields.Date(
        string='Test Date',
        help='Date to use for testing webhook reports. Leave empty to use current date in company timezone.'
    )

    @api.model
    def _get_timezone_selection(self):
        """Get list of available timezones"""
        return [(tz, tz) for tz in pytz.all_timezones if not tz.startswith('Etc/')]

    def _get_report_date(self):
        """Get the date to use for reports (test date or current date in company timezone)"""
        self.ensure_one()

        # If test date is set, use it
        if self.daily_webhook_test_date:
            return self.daily_webhook_test_date

        # Otherwise, get current date in company's timezone
        from datetime import datetime
        import pytz

        # Get company timezone
        company_tz = pytz.timezone(self.daily_webhook_timezone or 'UTC')

        # Get current datetime in company's timezone
        utc_now = pytz.UTC.localize(datetime.utcnow())
        company_now = utc_now.astimezone(company_tz)

        # Return just the date part
        return company_now.date()

    def _get_current_time_in_company_timezone(self):
        """Get current time in company timezone"""
        self.ensure_one()
        from datetime import datetime
        import pytz

        # Get company timezone
        company_tz = pytz.timezone(self.daily_webhook_timezone or 'UTC')

        # Get current datetime in company's timezone
        utc_now = pytz.UTC.localize(datetime.utcnow())
        company_now = utc_now.astimezone(company_tz)

        return company_now

    def _calculate_next_execution_time(self):
        """Calculate next execution time in UTC for the cron job"""
        self.ensure_one()
        from datetime import datetime, timedelta
        import pytz

        # Get company timezone
        company_tz = pytz.timezone(self.daily_webhook_timezone or 'UTC')

        # Get current time in company timezone
        utc_now = pytz.UTC.localize(datetime.utcnow())
        company_now = utc_now.astimezone(company_tz)

        # Create target time for today in company timezone
        target_time = company_now.replace(
            hour=self.daily_webhook_hour or 23,
            minute=self.daily_webhook_minute or 30,
            second=0,
            microsecond=0
        )

        # If target time has passed today, schedule for tomorrow
        if target_time <= company_now:
            target_time += timedelta(days=1)

        # Convert back to UTC for cron job
        target_utc = target_time.astimezone(pytz.UTC)

        return target_utc.replace(tzinfo=None)  # Remove timezone info for Odoo

    def _update_webhook_cron_schedule(self):
        """Update the main cron job schedule when settings change"""
        if not self.daily_webhook_enabled:
            # If webhook is disabled, keep cron job active but it will skip execution
            # This prevents the cron job from being deactivated unnecessarily
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Webhook disabled for {self.name} - cron job will skip execution")
            return

        # Find the main webhook cron job
        cron_job = self.env['ir.cron'].search([
            ('name', 'ilike', 'Daily Sales Webhook Reports')
        ], limit=1)

        if cron_job:
            next_execution = self._calculate_next_execution_time()

            # Update cron job with correct settings
            cron_job.sudo().write({
                'nextcall': next_execution,
                'active': True,  # Always keep active
                'interval_number': 1,
                'interval_type': 'days',
                'code': 'model.process_daily_reports_single_company()',
            })

            # Log the update
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"📅 Updated webhook cron schedule for {self.name}: next execution at {next_execution} UTC (= {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} {self.daily_webhook_timezone or 'UTC'})")
        else:
            # Log error if cron job not found
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error("❌ Daily Sales Webhook cron job not found! Please reinstall the module.")

    @api.constrains('daily_webhook_hour')
    def _check_webhook_hour(self):
        for record in self:
            if record.daily_webhook_hour < 0 or record.daily_webhook_hour > 23:
                raise ValidationError(_('Report hour must be between 0 and 23.'))
        # Update cron schedule when hour changes
        self._update_webhook_cron_schedule()

    @api.constrains('daily_webhook_minute')
    def _check_webhook_minute(self):
        for record in self:
            if record.daily_webhook_minute < 0 or record.daily_webhook_minute > 59:
                raise ValidationError(_('Report minute must be between 0 and 59.'))
        # Update cron schedule when minute changes
        self._update_webhook_cron_schedule()

    @api.constrains('daily_webhook_url')
    def _check_webhook_url(self):
        for record in self:
            if record.daily_webhook_url:
                # Basic URL validation
                url_pattern = re.compile(
                    r'^https?://'  # http:// or https://
                    r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
                    r'localhost|'  # localhost...
                    r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
                    r'(?::\d+)?'  # optional port
                    r'(?:/?|[/?]\S+)$', re.IGNORECASE)
                
                if not url_pattern.match(record.daily_webhook_url):
                    raise ValidationError(_('Please enter a valid webhook URL (must start with http:// or https://).'))

    @api.constrains('daily_webhook_retry_count')
    def _check_retry_count(self):
        for record in self:
            if record.daily_webhook_retry_count < 0 or record.daily_webhook_retry_count > 10:
                raise ValidationError(_('Retry count must be between 0 and 10.'))

    @api.constrains('daily_webhook_retry_delay')
    def _check_retry_delay(self):
        for record in self:
            if record.daily_webhook_retry_delay < 1 or record.daily_webhook_retry_delay > 60:
                raise ValidationError(_('Retry delay must be between 1 and 60 minutes.'))

    @api.constrains('daily_webhook_timezone')
    def _check_webhook_timezone(self):
        for record in self:
            if record.daily_webhook_timezone:
                try:
                    pytz.timezone(record.daily_webhook_timezone)
                except pytz.exceptions.UnknownTimeZoneError:
                    raise ValidationError(_('Invalid timezone selected.'))
        # Update cron schedule when timezone changes
        self._update_webhook_cron_schedule()

    @api.constrains('daily_webhook_enabled')
    def _check_webhook_enabled(self):
        # Update cron schedule when webhook is enabled/disabled
        for record in self:
            if record.daily_webhook_enabled:
                # When enabling webhook for the first time, ensure cron job is properly configured
                record._ensure_cron_job_configured()
            record._update_webhook_cron_schedule()

    def _ensure_cron_job_configured(self):
        """Ensure the cron job is properly configured for daily execution"""
        self.ensure_one()

        cron_job = self.env['ir.cron'].search([
            ('name', 'ilike', 'Daily Sales Webhook Reports')
        ], limit=1)

        if cron_job:
            # Check if it's misconfigured (running every minute)
            if cron_job.interval_number == 1 and cron_job.interval_type == 'minutes':
                # Fix the configuration
                cron_job.sudo().write({
                    'interval_number': 1,
                    'interval_type': 'days',
                    'code': 'model.process_daily_reports_single_company()',
                })

                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"🔧 Auto-fixed cron job configuration for {self.name} - changed from every minute to daily")
        else:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.warning(f"❌ Cron job not found for {self.name} - please reinstall the module")

    def action_test_webhook(self):
        """Test the webhook connection"""
        self.ensure_one()
        if not self.daily_webhook_url:
            raise ValidationError(_('Please configure the webhook URL first.'))
        
        # Import here to avoid circular imports
        webhook_service = self.env['daily.sales.webhook.service']
        
        # Create test data with demo values for n8n workflow configuration
        test_data = {
            'company_name': self.name,
            'date': fields.Date.today().strftime('%Y-%m-%d'),
            'total_sales': 25750.50,
            'total_customer_payments': 18300.75,
            'total_vendor_payments': 12500.25,
            'total_expenses': 4200.00,
            'total_cash': 5200.00,
            'cash_balances': {
                'Cash Register - Main': 3500.00,
                'Petty Cash': 500.00,
                'Cash Drawer': 1200.00
            },
            'total_bank': 75500.00,
            'bank_balances': {
                'Bank Account - ABC Bank': 45000.00,
                'Bank Account - XYZ Bank': 28000.00,
                'Credit Card Terminal': 2500.00
            },
            'expense_details': {
                'Office Supplies': 800.00,
                'Transportation Expenses': 1200.00,
                'Utilities': 1500.00,
                'Marketing Expenses': 700.00
            }
        }
        
        try:
            success = webhook_service.send_webhook_data(self, test_data)
            if success:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Webhook test successful!'),
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Webhook test failed. Check the error log.'),
                        'type': 'danger',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Webhook test failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_force_daily_report(self):
        """Force generate and send daily report for this company"""
        self.ensure_one()
        if not self.daily_webhook_url:
            raise ValidationError(_('Please configure the webhook URL first.'))

        # Force create daily report and send webhook using sudo for permissions
        report_model = self.env['daily.sales.report'].sudo()

        try:
            # Get the report date (test date or current date in user timezone)
            report_date = self._get_report_date()

            # Create daily report for the determined date
            report = report_model.create_daily_report(self.id, report_date)

            # Send webhook after record is created (with proper data format)
            success = report.send_webhook()

            if success:
                # Include debug info in success message
                success_message = f"Daily report sent successfully!\n\nReport Date: {report_date}\nCompany: {self.name}\nWebhook Status: {'✅ Sent' if success else '❌ Failed'}"
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('📊 Success - Daily Report Debug'),
                        'message': success_message,
                        'type': 'success',
                        'sticky': True,
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Daily report failed. Check the error log.'),
                        'type': 'danger',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Daily report failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_debug_webhook(self):
        """Debug webhook configuration and test account balance calculation"""
        self.ensure_one()

        debug_info = []
        debug_info.append(f"Company: {self.name}")
        debug_info.append(f"Webhook Enabled: {self.daily_webhook_enabled}")
        debug_info.append(f"Webhook URL: {self.daily_webhook_url or 'NOT SET'}")
        debug_info.append(f"Timezone: {self.daily_webhook_timezone or 'UTC'}")
        debug_info.append(f"Schedule: {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} ({self.daily_webhook_timezone or 'UTC'})")

        # Show current time in company timezone
        try:
            company_time = self._get_current_time_in_company_timezone()
            debug_info.append(f"Current Time: {company_time.strftime('%H:%M')} ({self.daily_webhook_timezone or 'UTC'})")
        except Exception as e:
            debug_info.append(f"Current Time: Error - {str(e)}")

        debug_info.append("")

        # Test account balance calculation
        try:
            cash_journals = self.env['account.journal'].search([
                ('company_id', '=', self.id),
                ('type', '=', 'cash')
            ], limit=3)

            bank_journals = self.env['account.journal'].search([
                ('company_id', '=', self.id),
                ('type', '=', 'bank')
            ], limit=3)

            debug_info.append("Cash Journals:")
            for journal in cash_journals:
                if journal.default_account_id:
                    try:
                        balance = self.env['daily.sales.report'].sudo()._get_account_balance(
                            journal.default_account_id, self.id, fields.Date.today()
                        )
                        debug_info.append(f"  ✅ {journal.name}: {balance}")
                    except Exception as e:
                        debug_info.append(f"  ❌ {journal.name}: {str(e)}")
                else:
                    debug_info.append(f"  ⚠️ {journal.name}: No default account")

            debug_info.append("")
            debug_info.append("Bank Journals:")
            for journal in bank_journals:
                if journal.default_account_id:
                    try:
                        balance = self.env['daily.sales.report'].sudo()._get_account_balance(
                            journal.default_account_id, self.id, fields.Date.today()
                        )
                        debug_info.append(f"  ✅ {journal.name}: {balance}")
                    except Exception as e:
                        debug_info.append(f"  ❌ {journal.name}: {str(e)}")
                else:
                    debug_info.append(f"  ⚠️ {journal.name}: No default account")

        except Exception as e:
            debug_info.append(f"Error testing balances: {str(e)}")

        message = "\n".join(debug_info)

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Webhook Debug Info'),
                'message': message,
                'type': 'info',
                'sticky': True,
            }
        }

    def action_test_cron_job(self):
        """Test the cron job execution manually"""
        self.ensure_one()

        try:
            # Test the cron job method directly
            service = self.env['daily.sales.webhook.service']

            # Force execution (ignores timing)
            result = service.force_process_daily_reports()

            # Get current time info in company timezone
            company_current_time = self._get_current_time_in_company_timezone()

            # Get next execution time
            next_execution = self._calculate_next_execution_time()

            message = f"""Cron Job Test Results (Single Company Mode):

📊 PROCESSING SUMMARY:
• Companies Processed: {result["processed"]}
• Successful Webhooks: {result["success"]}
• Failed Webhooks: {result["errors"]}

⏰ TIMING INFO:
• Company Timezone: {self.daily_webhook_timezone or 'UTC'}
• Current Time: {company_current_time.strftime('%H:%M')} ({self.daily_webhook_timezone or 'UTC'})
• Your Schedule: {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} ({self.daily_webhook_timezone or 'UTC'})
• Next Execution: {next_execution.strftime('%Y-%m-%d %H:%M')} UTC
• Webhook Enabled: {'✅ Yes' if self.daily_webhook_enabled else '❌ No'}

📋 NEXT STEPS:
• Check n8n executions for webhook data
• Verify "Last Report Sent" timestamp
• Check Daily Reports in Technical menu
• Cron job will run automatically at scheduled time"""

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Cron Job Test Results'),
                    'message': message,
                    'type': 'success' if result["errors"] == 0 else 'warning',
                    'sticky': True,
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Cron Test Error'),
                    'message': f'Error testing cron job: {str(e)}',
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_reset_daily_webhook(self):
        """Reset today's webhook status to allow resending"""
        self.ensure_one()

        try:
            # Find report for the test/current date
            report_date = self._get_report_date()
            today_report = self.env['daily.sales.report'].sudo().search([
                ('company_id', '=', self.id),
                ('report_date', '=', report_date)
            ], limit=1)

            if today_report:
                old_status = today_report.webhook_sent
                today_report.webhook_sent = False
                today_report.webhook_error = False

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Webhook Reset'),
                        'message': _('Today\'s webhook status reset from %s to False. Cron job will try again.') % old_status,
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('No Report Found'),
                        'message': _('No daily report found for today. Cron job will create a new one.'),
                        'type': 'info',
                    }
                }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Reset Error'),
                    'message': _('Error resetting webhook: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_debug_daily_data(self):
        """Debug daily data calculation and show results"""
        self.ensure_one()

        try:
            report_model = self.env['daily.sales.report']
            report_date = self._get_report_date()

            # Also check if there are any payments/expenses in the last 7 days
            from datetime import timedelta
            week_ago = report_date - timedelta(days=7)

            # Check for recent payments
            recent_customer_payments = self.env['account.payment'].search([
                ('company_id', '=', self.id),
                ('payment_type', '=', 'inbound'),
                ('partner_type', '=', 'customer'),
                ('state', '=', 'posted'),
                ('date', '>=', week_ago),
            ], limit=5, order='date desc')

            recent_vendor_payments = self.env['account.payment'].search([
                ('company_id', '=', self.id),
                ('payment_type', '=', 'outbound'),
                ('partner_type', '=', 'supplier'),
                ('state', '=', 'posted'),
                ('date', '>=', week_ago),
            ], limit=5, order='date desc')

            # Run calculation
            daily_data = report_model.calculate_daily_data(self.id, report_date)



            # Format results
            debug_info = []
            debug_info.append(f"Company: {self.name}")
            debug_info.append(f"Date: {report_date}")
            debug_info.append("")
            debug_info.append("📊 CALCULATED DATA:")
            debug_info.append(f"Total Sales: {daily_data['total_sales']}")
            debug_info.append(f"Customer Payments: {daily_data['total_payments']}")
            debug_info.append(f"Vendor Payments: {daily_data['total_vendor_payments']}")
            debug_info.append(f"Total Expenses: {daily_data['total_expenses']}")
            debug_info.append(f"Total Cash: {daily_data['total_cash']}")
            debug_info.append(f"Total Bank: {daily_data['total_bank']}")
            debug_info.append("")
            debug_info.append("💵 CASH BALANCES:")
            for name, balance in daily_data['cash_balances'].items():
                debug_info.append(f"  {name}: {balance}")
            debug_info.append("")
            debug_info.append("🏦 BANK BALANCES:")
            for name, balance in daily_data['bank_balances'].items():
                debug_info.append(f"  {name}: {balance}")
            debug_info.append("")
            debug_info.append("🧾 EXPENSE DETAILS:")
            for account, amount in daily_data['expense_details'].items():
                debug_info.append(f"  {account}: {amount}")

            # Add recent payments info
            debug_info.append("")
            debug_info.append("📋 RECENT CUSTOMER PAYMENTS (Last 7 days):")
            if recent_customer_payments:
                for payment in recent_customer_payments:
                    debug_info.append(f"  {payment.date}: {payment.amount} ({payment.partner_id.name})")
            else:
                debug_info.append("  No recent customer payments found")

            debug_info.append("")
            debug_info.append("📋 RECENT VENDOR PAYMENTS (Last 7 days):")
            if recent_vendor_payments:
                for payment in recent_vendor_payments:
                    debug_info.append(f"  {payment.date}: {payment.amount} ({payment.partner_id.name})")
            else:
                debug_info.append("  No recent vendor payments found")

            message = "\n".join(debug_info)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Daily Data Debug'),
                    'message': message,
                    'type': 'info',
                    'sticky': True,
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Debug Error'),
                    'message': f'Error debugging data: {str(e)}',
                    'type': 'danger',
                    'sticky': True,
                }
            }

    def action_clear_debug_info(self):
        """Clear debug information from recent reports"""
        self.ensure_one()

        # Find recent reports with debug info
        reports_with_debug = self.env['daily.sales.report'].search([
            ('company_id', '=', self.id),
            ('webhook_error', 'ilike', 'DEBUG INFO')
        ])

        if reports_with_debug:
            reports_with_debug.write({'webhook_error': False})
            message = f'Cleared debug info from {len(reports_with_debug)} reports.'
        else:
            message = 'No reports with debug info found.'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': '🧹 Debug Info Cleared',
                'message': message,
                'type': 'success',
            }
        }

    def action_update_cron_schedule(self):
        """Manually update the cron job schedule"""
        self.ensure_one()

        if not self.daily_webhook_enabled:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Webhook Disabled'),
                    'message': _('Please enable webhook first.'),
                    'type': 'warning',
                }
            }

        try:
            # Update cron schedule
            self._update_webhook_cron_schedule()

            # Get next execution time for display
            next_execution = self._calculate_next_execution_time()
            company_time = self._get_current_time_in_company_timezone()

            message = f"""Cron Schedule Updated Successfully!

⏰ SCHEDULE INFO:
• Current Time: {company_time.strftime('%H:%M')} ({self.daily_webhook_timezone or 'UTC'})
• Your Schedule: {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} ({self.daily_webhook_timezone or 'UTC'})
• Next Execution: {next_execution.strftime('%Y-%m-%d %H:%M')} UTC

✅ The cron job will now run automatically at your scheduled time."""

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('📅 Cron Schedule Updated'),
                    'message': message,
                    'type': 'success',
                    'sticky': True,
                }
            }

        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Update Error'),
                    'message': _('Error updating cron schedule: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_fix_cron_job(self):
        """Fix the cron job configuration if it's running every minute"""
        self.ensure_one()

        # Find the main webhook cron job
        cron_job = self.env['ir.cron'].search([
            ('name', 'ilike', 'Daily Sales Webhook Reports')
        ], limit=1)

        if not cron_job:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Cron Job Not Found'),
                    'message': _('Daily Sales Webhook cron job not found. Please reinstall the module.'),
                    'type': 'danger',
                }
            }

        # Check current configuration
        current_interval = cron_job.interval_number
        current_type = cron_job.interval_type
        current_code = cron_job.code

        # Fix the configuration
        if current_interval == 1 and current_type == 'minutes':
            # This is the problem - fix it
            next_execution = self._calculate_next_execution_time()

            cron_job.sudo().write({
                'interval_number': 1,
                'interval_type': 'days',
                'code': 'model.process_daily_reports_single_company()',
                'nextcall': next_execution,
                'active': self.daily_webhook_enabled,
            })

            message = f"""✅ Cron Job Fixed Successfully!

🔧 CHANGES MADE:
• Interval: {current_interval} {current_type} → 1 days
• Code: {current_code} → model.process_daily_reports_single_company()
• Next Execution: {next_execution.strftime('%Y-%m-%d %H:%M')} UTC
• Status: {'Active' if self.daily_webhook_enabled else 'Inactive'}

⏰ SCHEDULE INFO:
• Your Time: {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} ({self.daily_webhook_timezone or 'UTC'})
• Next Run: {next_execution.strftime('%Y-%m-%d %H:%M')} UTC

🎉 The cron job will now run once per day at your scheduled time instead of every minute!"""

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('🔧 Cron Job Fixed!'),
                    'message': message,
                    'type': 'success',
                    'sticky': True,
                }
            }
        else:
            # Already configured correctly
            message = f"""✅ Cron Job Already Configured Correctly!

📊 CURRENT CONFIGURATION:
• Interval: {current_interval} {current_type}
• Code: {current_code}
• Next Execution: {cron_job.nextcall}
• Status: {'Active' if cron_job.active else 'Inactive'}

⏰ SCHEDULE INFO:
• Your Time: {self.daily_webhook_hour:02d}:{self.daily_webhook_minute:02d} ({self.daily_webhook_timezone or 'UTC'})

✅ No changes needed - the cron job is properly configured."""

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('✅ Cron Job Status'),
                    'message': message,
                    'type': 'info',
                    'sticky': True,
                }
            }
