# New Installation Guide - Daily Sales Webhook

## ✅ **For NEW Installations**

### **Automatic Configuration**
With the latest updates, **new installations will be configured correctly automatically**:

1. **During Installation**:
   - ✅ Cron job created with **1 Days** interval (not minutes)
   - ✅ Correct method: `model.process_daily_reports_single_company()`
   - ✅ Initially **inactive** (will activate when user enables webhook)
   - ✅ Post-install hook ensures proper configuration

2. **When User First Enables Webhook**:
   - ✅ Auto-detects and fixes any misconfiguration
   - ✅ Sets cron job to user's preferred time
   - ✅ Activates the cron job
   - ✅ No manual intervention needed

### **What Users Need to Do**
For new installations, users only need to:

1. **Go to Settings → Companies → [Company] → Daily Webhook Settings**
2. **Enable webhook** ✅
3. **Set webhook URL** ✅
4. **Choose timezone** (e.g., "Asia/Riyadh") ✅
5. **Set preferred time** (e.g., 23:30) ✅
6. **Click "Update Cron Schedule"** ✅

**Result**: Cron job will run once daily at the exact time specified!

## 🔧 **For EXISTING Installations (Upgrade)**

### **Automatic Fix**
When upgrading existing installations:

1. **Auto-Detection**: System detects if cron job is running every minute
2. **Auto-Fix**: When user enables webhook, it automatically fixes the configuration
3. **Manual Fix**: "Fix Cron Job" button available for immediate fixing

### **What Users Need to Do**
For existing installations with the every-minute issue:

**Option 1: Use Fix Button (Recommended)**
1. Go to company settings
2. Click **"Fix Cron Job"** button
3. System will automatically detect and fix the issue

**Option 2: Use Fix Script**
```python
# In Odoo shell
exec(open('fix_cron_job_immediately.py').read())
```

**Option 3: Automatic Fix**
- Simply enable/disable webhook in company settings
- System will auto-detect and fix the configuration

## 📊 **Configuration Verification**

### **How to Check if Cron Job is Correct**
1. Go to **Settings → Technical → Automation → Scheduled Actions**
2. Find **"Daily Sales Webhook Reports"**
3. Check:
   - ✅ **Execute Every**: 1 **Days** (not Minutes)
   - ✅ **Code**: `model.process_daily_reports_single_company()`
   - ✅ **Next Execution**: Set to your preferred time

### **Expected Configuration**
```
Name: Daily Sales Webhook Reports
Execute Every: 1 Days
Code: model.process_daily_reports_single_company()
Active: True (when webhook enabled)
Next Execution: [Your preferred time in UTC]
```

## 🎯 **Summary**

| Installation Type | Manual Fix Needed? | What Happens |
|------------------|-------------------|--------------|
| **New Installation** | ❌ **NO** | Automatically configured correctly |
| **Existing (Upgrade)** | ⚠️ **Maybe** | Auto-fix when enabling webhook, or use "Fix Cron Job" button |

## 🚀 **Key Improvements Made**

1. **Post-Install Hook**: Ensures new installations are configured correctly
2. **Auto-Detection**: System detects and fixes every-minute configuration
3. **Auto-Fix on Enable**: When user enables webhook, system auto-fixes cron job
4. **Manual Fix Button**: "Fix Cron Job" button for immediate fixing
5. **Enhanced Validation**: Multiple layers of validation and auto-correction

## 🎉 **Result**

- **New installations**: Work perfectly out of the box
- **Existing installations**: Easy one-click fix
- **No more every-minute spam**: Cron job runs once daily at user's preferred time
- **Perfect timing**: Reports sent at exact user time in their timezone

The system is now bulletproof for both new and existing installations! 🛡️
